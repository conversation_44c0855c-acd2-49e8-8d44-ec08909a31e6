
# from .get_db import db
from .resolve_llm import resolve_llm

# from .track_time import time_it

# @time_it
# def sentiment_classification(data):
#     latest_message_str = data["latest_message_str"]
#     try:
#         prompt_db = db["prompt"] # variable naming bhayena
#         sentiment_classification_prompt = prompt_db.find_one({"name":"sentiment_classification"}) # k ho yo jepayetei naming?
#         prompt_ = sentiment_classification_prompt["text"]
#         model = sentiment_classification_prompt["model"]
#         llm= resolve_llm(model_name=model)
#         sentiment_prompt=prompt_.format(question=latest_message_str)
#         response = llm.complete(prompt=sentiment_prompt,formatted=True)
#         return response.text.strip(" ").split(":")[-1]
#     except Exception as e:
#         # loggers.error(f"Error classifying sentiment: {e}") # i dont see the point of this error log
#         raise e



from pydantic import BaseModel

class LanguageResponse(BaseModel):
    detected_language: str


# @time_it
from pydantic import BaseModel

class LanguageResponse(BaseModel):
    detected_language: str

def detect_language(data, current_user):
    latest_message_str = data["latest_message_str"]
    language_prompt = data["prompt"]
    
    try:
        prompt_ = language_prompt["text"]
        model = language_prompt["model"]
        
        llm = resolve_llm(model_name=model, current_user=current_user)
        language_prompt = prompt_.format(question=latest_message_str)
        print("\n\n language_prompt",language_prompt)
        response = llm.complete(prompt=language_prompt, formatted=True)

        print("language sentti",response)
        
        # Flexible response parsing
        try:
            cleaned_text = response.text.split(":", 1)[-1].strip()
        except Exception:
            cleaned_text = response.text  # fallback to raw text
        
        # Convert to new format
        usage = response.raw.usage.model_dump() if hasattr(response.raw, 'usage') else {}
        
        return {
            "result": {"detected_language": cleaned_text},
            "usage": usage
        }
        
    except Exception as e:
        return {
            "result": None,
            "usage": {},
            "error": str(e)
        }